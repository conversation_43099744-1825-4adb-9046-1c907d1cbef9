# E2E Tests for Consumer Portal

This directory contains end-to-end (e2e) tests for the Tallied Consumer Portal using Playwright.

## Overview

The e2e tests are designed to run against the deployed preprod environment at:
`https://consumer-portal.preprod.tallied.io`

## Test Structure

### Core Authentication & Login Tests

#### `login.spec.ts`
Contains regression tests for the login functionality:

- **Login Flow Test**: Tests the complete login process including:
  - Navigation to login page
  - Clicking "Login with email"
  - Entering credentials on Zitadel OAuth provider
  - Successful authentication and redirect

- **Page Display Test**: Verifies the login page loads correctly

### Dashboard & UI Verification Tests

#### `dashboard-verification.spec.ts`
Basic UI verification tests for the dashboard:

- **Navigation Elements**: Verifies Home, Payments, Rewards navigation is visible and functional
- **Tab Switching**: Tests switching between "Recent Activity" and "Statements" tabs
- **Page Navigation**: Tests navigation to Payments and Rewards pages
- **Content Structure**: Verifies key financial information is displayed correctly
- **Element Visibility**: Ensures elements are visible to users (not just in DOM)
- **PDF Functionality**: Tests that statement PDFs open correctly when clicked

#### `dashboard-comprehensive.spec.ts`
Comprehensive dashboard interaction and functionality tests:

- **Dashboard Content Verification**: Tests all dashboard elements and content structure
- **Tab Switching Logic**: Advanced tab switching between "Recent Activity" and "Statements"
- **Interactive Elements**: Tests buttons, links, and interactive components
- **PDF Download & Verification**: Downloads and verifies PDF structure and content
- **Statement Processing**: Verifies statement links and PDF accessibility
- **Content Organization**: Ensures proper layout and content hierarchy

#### `ui-exploration.spec.ts`
Comprehensive UI element discovery and interaction tests:

- **Interactive Element Discovery**: Automatically discovers and catalogs all interactive elements
- **Element State Verification**: Tests visibility, enabled state, and accessibility
- **Cross-Page Element Mapping**: Maps interactive elements across all pages
- **Modal and Drawer Detection**: Identifies and tests modal/drawer triggers
- **Screenshot Documentation**: Captures full-page screenshots for visual verification
- **Element Classification**: Categorizes elements by type (buttons, links, inputs, etc.)

### Navigation & Flow Tests

#### `navigation-flow.spec.ts`
Basic navigation flow and user journey tests:

- **Multi-page Navigation**: Tests navigation through Home → Payments → Rewards → Home
- **Tab Functionality**: Verifies tab switching works correctly on home page
- **Navigation State**: Tests that navigation state persists correctly
- **Interactive Elements**: Ensures navigation remains functional after interactions

#### `navigation-comprehensive.spec.ts`
Comprehensive navigation testing across all scenarios:

- **Circular Navigation Flow**: Tests complete navigation cycles through all pages
- **Navigation Link Verification**: Verifies all navigation links have correct hrefs
- **Tab State Management**: Advanced tab switching and state persistence
- **Mobile Navigation**: Tests hamburger menu and mobile navigation patterns
- **Navigation Error Handling**: Tests navigation during loading states and rapid transitions
- **Cross-Viewport Navigation**: Ensures navigation works across desktop and mobile viewports
- **Navigation Accessibility**: Verifies navigation remains accessible across viewport changes

### Payment System Tests

#### `payment-comprehensive.spec.ts`
Comprehensive payment system functionality tests:

- **Payment Page Navigation**: Tests navigation to and within payment pages
- **My Linked Accounts Integration**: Tests account linking and management functionality
- **Payment Method Management**: Tests adding, removing, and managing payment methods
- **Account Removal Flow**: Tests the complete account removal process and error handling
- **Bank Account Linking**: Tests MX integration and manual bank account linking
- **Micro-deposit Verification**: Tests manual account verification flow
- **Payment Error Handling**: Tests error scenarios and user feedback

#### `payment-linked-accounts.spec.ts`
Specialized tests for linked account management:

- **Account Details Verification**: Tests display of account information and last 4 digits
- **Remove Account Flow**: Tests account removal process and "Unable to Remove Account" error
- **Link New Account Modal**: Tests the "Link My Bank Account" modal functionality
- **MX Bank Selection**: Tests bank selection through MX integration
- **Manual Linking Process**: Tests manual account linking with micro-deposits
- **Account Requirement Validation**: Ensures at least one account remains linked
- **Modal Cancel Functionality**: Tests cancel buttons and modal dismissal

#### `payment-page-exploration.spec.ts`
Exploratory tests for payment page elements and interactions:

- **Element Discovery**: Automatically discovers payment page interactive elements
- **My Linked Accounts Detection**: Tests various selectors for account management components
- **Interactive Element Testing**: Tests clicking and interaction with discovered elements
- **Screenshot Documentation**: Captures payment page state for visual verification
- **Selector Validation**: Tests multiple selector strategies for robust element detection

### Rewards System Tests

#### `rewards-comprehensive.spec.ts`
Comprehensive rewards system functionality tests:

- **Rewards Page Content**: Tests rewards balance display and reward options
- **Cashback Functionality**: Tests "Get Cashback" card and redemption flow
- **Travel Rewards**: Tests travel rewards card and booking functionality
- **Rewards Balance Verification**: Tests rewards balance display and calculations
- **Redemption Process**: Tests complete reward redemption workflows
- **Rewards Navigation Integration**: Tests navigation from dashboard to rewards
- **Mobile Rewards Experience**: Tests rewards functionality on mobile devices
- **Rewards Error Handling**: Tests insufficient balance scenarios and error messages
- **Visual Elements**: Tests rewards icons and visual components

### Modal & Drawer Tests

#### `modals-drawers-comprehensive.spec.ts`
Comprehensive testing of modal and drawer functionality:

- **Settings Menu Integration**: Tests settings menu opening and navigation
- **My Account Drawer**: Tests account information drawer functionality
- **Documents Drawer**: Tests document access and management
- **Card Management Drawer**: Tests card management options and actions
- **Drawer Overlay Behavior**: Tests persistent drawer behavior and overlay clicks
- **Modal Escape Key Functionality**: Tests keyboard navigation and modal dismissal
- **Mobile Modal Behavior**: Tests modal positioning and touch interactions on mobile
- **Session Management Modals**: Tests session timeout and continuation modals
- **Modal Viewport Coverage**: Tests modal sizing and viewport coverage on different devices

### PDF Content & Document Tests

#### `pdf-content-verification.spec.ts`
Specialized tests for PDF content and document structure:

- **PDF Download & Structure**: Downloads PDFs and verifies internal structure
- **Statement PDF Verification**: Tests statement PDF accessibility and content
- **PDF Link Validation**: Verifies PDF links are present and functional
- **Content Organization**: Ensures PDF content is properly organized with headers
- **URL Decoding**: Handles URL-encoded PDF links correctly

### Responsive Design Tests

#### `responsive-design.spec.ts`
Basic responsive design testing across different viewports:

- **Desktop Tests (1920x1080)**: Tests standard desktop navigation and functionality
- **Mobile Tests (2868x1320)**: Tests mobile navigation including hamburger menu functionality
- **Viewport Switching**: Tests behavior when switching between desktop and mobile viewports
- **Cross-Resolution PDF Testing**: Verifies PDF functionality works on both desktop and mobile
- **Navigation Consistency**: Ensures navigation works correctly across different screen sizes

#### `responsive-comprehensive.spec.ts`
Comprehensive responsive design and cross-platform testing:

- **Multi-Viewport Testing**: Tests functionality across desktop, tablet, and mobile viewports
- **Responsive Navigation**: Tests both standard navigation and hamburger menu patterns
- **Mobile-First Interactions**: Tests touch interactions and mobile-specific UI patterns
- **Cross-Platform PDF Handling**: Tests PDF functionality across different devices
- **Viewport Transition Testing**: Tests smooth transitions between different viewport sizes
- **Mobile Drawer Behavior**: Tests drawer positioning and behavior on mobile devices

### Utility Functions

#### `test-utils.ts`
Comprehensive utility functions and helpers for all tests:

- **Authentication Helpers**: `loginToPortal()` with retry logic and error handling
- **Navigation Utilities**: `navigateToPage()`, `switchToTab()`, `verifyNavigation()`
- **Element Interaction**: `safeClick()`, `waitForElementVisible()`, `waitForPageStable()`
- **Payment System Helpers**: Account linking, removal, and MX integration utilities
- **PDF Processing**: `downloadAndVerifyPdf()`, `verifyPdfLink()`, PDF structure verification
- **Responsive Helpers**: `isMobileNavigation()`, `accessMobileNavigation()`, viewport utilities
- **Modal/Drawer Utilities**: Opening, closing, and verifying modal/drawer states
- **Content Verification**: Text content verification and element state checking
- **Configuration Constants**: Viewports, timeouts, element selectors, and test credentials

## Test Coverage Summary

The e2e test suite now includes **15 test specification files** covering comprehensive functionality:

### 📊 **Test Categories**
- **Authentication & Login**: 1 test file (2 tests)
- **Dashboard & UI**: 3 test files (comprehensive dashboard, verification, and UI exploration)
- **Navigation**: 2 test files (basic flow and comprehensive navigation)
- **Payment System**: 3 test files (comprehensive payments, linked accounts, and exploration)
- **Rewards System**: 1 test file (comprehensive rewards functionality)
- **Modals & Drawers**: 1 test file (comprehensive modal/drawer testing)
- **Responsive Design**: 2 test files (basic and comprehensive responsive testing)
- **PDF & Documents**: 1 test file (PDF content verification)
- **Utility Functions**: 1 test file (shared utilities and helpers)

### ✅ **Core Functionality Covered**
- ✅ **Authentication Flow**: Complete OAuth login with Zitadel integration
- ✅ **Navigation Systems**: Both desktop navigation and mobile hamburger menu
- ✅ **Dashboard Interactions**: Tab switching, content verification, and interactive elements
- ✅ **Payment Management**: Account linking, removal, MX integration, and micro-deposits
- ✅ **Rewards System**: Balance display, redemption flows, and error handling
- ✅ **Modal/Drawer Systems**: Settings, account management, and document access
- ✅ **PDF Functionality**: Statement downloads, content verification, and structure validation
- ✅ **Responsive Design**: Cross-platform testing on desktop and mobile viewports
- ✅ **Error Handling**: Comprehensive error scenarios and user feedback testing

### 🔧 **Technical Features**
- **Viewport Consistency**: All tests use consistent viewport sizes (1920x1080 for desktop, 2868x1320 for mobile)
- **PDF Processing**: Advanced PDF download and content structure verification
- **Mobile-First Testing**: Touch interactions and mobile-specific UI patterns
- **Retry Logic**: Robust error handling and retry mechanisms for flaky operations
- **Screenshot Documentation**: Comprehensive visual documentation of test states
- **Cross-Browser Support**: Tests configured for Chromium, Firefox, and WebKit

### ⚠️ **Known Considerations**
- **PDF Content Extraction**: PDF content extraction may fail for image-based PDFs (expected behavior)
- **Mobile Navigation**: Hamburger menu behavior varies based on CSS breakpoints and viewport size
- **MX Integration**: Bank selection and linking flows depend on external MX service availability
- **Session Management**: Session timeout modals are difficult to trigger in automated tests
- **Performance Timeouts**: Maximum 8-second timeout ensures tests fail for unacceptable UX performance

### 🎯 **Test Strategy**
- **Comprehensive Coverage**: Tests cover both happy path and error scenarios
- **User-Centric Approach**: Tests focus on actual user interactions and visible elements
- **Mobile-First Design**: Equal emphasis on desktop and mobile user experiences
- **Real-World Scenarios**: Tests use actual preprod environment with real data flows
- **Visual Verification**: Screenshot capture for manual verification of UI states

## Test Organization

### File Naming Convention
- **Basic Tests**: `[feature].spec.ts` (e.g., `login.spec.ts`)
- **Comprehensive Tests**: `[feature]-comprehensive.spec.ts` (e.g., `payment-comprehensive.spec.ts`)
- **Specialized Tests**: `[feature]-[specialization].spec.ts` (e.g., `payment-linked-accounts.spec.ts`)
- **Exploration Tests**: `[feature]-exploration.spec.ts` or `ui-exploration.spec.ts`

### Test Structure
Each test file follows a consistent structure:
- **Test Setup**: `beforeEach` hooks for login and viewport configuration
- **Test Groups**: Logical grouping using `test.describe()` blocks
- **Utility Integration**: Extensive use of shared utilities from `test-utils.ts`
- **Error Handling**: Comprehensive error scenarios and edge cases
- **Documentation**: Console logging and screenshot capture for debugging

## Running Tests

### Prerequisites
Make sure Playwright is installed and browsers are downloaded:
```bash
npm install
npx playwright install
```

### Run Commands

```bash
# Run all e2e tests (headless, all browsers)
npm run test:e2e

# Run tests with browser UI visible
npm run test:e2e:headed

# Run tests with Playwright UI for debugging
npm run test:e2e:ui

# Run tests for specific browser
npx playwright test --project=chromium
npx playwright test --project=firefox
npx playwright test --project=webkit

# Run specific test file
npx playwright test login.spec.ts
npx playwright test payment-comprehensive.spec.ts

# Run tests matching a pattern
npx playwright test --grep "payment"
npx playwright test --grep "responsive"
```

## Test Credentials

The tests use the following test account:
- **Email**: `<EMAIL>`
- **Password**: `10Apples!`

## Configuration

The Playwright configuration is in `playwright.config.ts` and includes:
- Base URL pointing to preprod environment
- Multiple browser configurations
- Screenshot and video capture on failures
- HTML reporting

## Authentication Flow

The tests handle the OAuth flow with Zitadel:
1. Click "Login with email" on the portal
2. Redirect to Zitadel login page
3. Enter username/email (first step)
4. Click "Next"
5. Enter password (second step)
6. Submit and redirect back to portal

## Debugging

- Test results and artifacts are saved in `test-results/`
- Screenshots and videos are captured on failures
- Use `--headed` flag to see browser during test execution
- Use `--ui` flag for interactive debugging with Playwright UI
- HTML report available at `http://localhost:9323` after test runs
